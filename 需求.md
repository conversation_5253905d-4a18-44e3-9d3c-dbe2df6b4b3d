技术栈：html、css、js/jQuery，编写纯静态页面
这是一个大学生的课后作业，作业的目的是要求巩固下面的技术
1、掌握javascript数组的综合应用；
2、掌握JavaScript对象的综合应用；
3、掌握JavaScript操作DOM的综合应用；
4、掌握JavaScript事件的综合应用；
5、掌握JavaScript中BOM的综合应用；
6、掌握jQuery选择器的综合应用；
7、掌握jQuery操作DOM的综合应用；
8、掌握jQuery事件的综合应用；
9、掌握jQuery动画的综合应用；
其它要求以及评分标准：
1、选题恰当，页面内容主题为“井冈山精神”，内容丰富，具有实际意义，无明显运行错误，工作量饱满；（10分）
2、JavaScript的基本使用与对象的综合使用，比如数组对象、字符串对象、数学对象、日期对象、正则表达式等；（20分）
3、DOM与事件的综合使用，比如：操作元素内容、属性、样式、节点等、鼠标事件、键盘事件、窗口事件等；（30分）
4、jQuery常用方法、JQuery操作DOM、Jquery事件的使用，比如：jQuery选择器、Class属性操作、HTML属性操作等、查找DOM、操作DOM、鼠标事件、表单事件、事件高级；（20分）
5、Jquery动画的使用，比如：淡入淡出，自定义动画等；（5分）
6.创新部分：Ajax工具使用；
